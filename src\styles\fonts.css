/* San Francisco Pro Font Definitions */

/* Utility classes for SF Pro fonts */

/* SF Pro Display - better for headings and larger text */
.font-sf-display {
  font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, "San Francisco", "Helvetica Neue", system-ui, sans-serif !important;
}

/* SF Pro Text - better for body text and smaller UI elements */
.font-sf {
  font-family: 'SF Pro Text', 'SF Pro Display', -apple-system, BlinkMacSystemFont, "San Francisco", "Helvetica Neue", system-ui, sans-serif !important;
}

/* We'll primarily use just regular and medium weights for a cleaner look */
.font-sf-regular {
  font-family: 'SF Pro Text', 'SF Pro Display', -apple-system, BlinkMacSystemFont, "San Francisco", "Helvetica Neue", system-ui, sans-serif !important;
  font-weight: 400 !important;
}

.font-sf-medium {
  font-family: 'SF Pro Text', 'SF Pro Display', -apple-system, BlinkMacSystemFont, "San Francisco", "Helvetica Neue", system-ui, sans-serif !important;
  font-weight: 500 !important;
}

/* SF Pro Display fonts */
@font-face {
  font-family: 'SF Pro Display';
  src: url('/fonts/SF-PRO/SF-Pro-Display-Regular.otf') format('opentype');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'SF Pro Display';
  src: url('/fonts/SF-PRO/SF-Pro-Display-Medium.otf') format('opentype');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

/* SF Pro Text fonts - better for smaller text */
@font-face {
  font-family: 'SF Pro Text';
  src: url('/fonts/SF-PRO/SF-Pro-Text-Regular.otf') format('opentype');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'SF Pro Text';
  src: url('/fonts/SF-PRO/SF-Pro-Text-Medium.otf') format('opentype');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}
